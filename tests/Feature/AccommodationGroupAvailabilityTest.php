<?php

namespace Tests\Feature;

use App\Models\Accommodation;
use App\Models\AccommodationGroup;
use App\Models\User;
use App\Models\Site;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class AccommodationGroupAvailabilityTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $user;
    private Site $site;
    private AccommodationGroup $group;
    private Accommodation $accommodation1;
    private Accommodation $accommodation2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user
        $this->user = User::factory()->create();

        // Create a site
        $this->site = Site::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // Create an accommodation group
        $this->group = AccommodationGroup::factory()->create([
            'user_id' => $this->user->id,
            'published' => true,
        ]);

        // Create accommodations in the group
        $this->accommodation1 = Accommodation::factory()->create([
            'user_id' => $this->user->id,
            'accommodation_group_id' => $this->group->id,
            'published' => true,
            'minimum_stay' => 1,
            'minimum_booking_notice' => 0,
            'min_occupancy' => 1,
            'max_occupancy' => 4,
        ]);

        $this->accommodation2 = Accommodation::factory()->create([
            'user_id' => $this->user->id,
            'accommodation_group_id' => $this->group->id,
            'published' => true,
            'minimum_stay' => 2,
            'minimum_booking_notice' => 1,
            'min_occupancy' => 2,
            'max_occupancy' => 6,
        ]);
    }

    public function test_can_check_group_availability_with_widget_auth()
    {
        // Create a widget token
        $token = $this->user->createToken('widget-token', ['widget-access']);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson("/api/accommodation-groups/{$this->group->id}/check-availability", [
            'start_date' => now()->addDays(5)->format('Y-m-d'),
            'end_date' => now()->addDays(7)->format('Y-m-d'),
            'number_of_persons' => 2,
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'group_id',
                'group_name',
                'has_availability',
                'total_accommodations',
                'available_accommodations',
                'accommodations' => [
                    '*' => [
                        'id',
                        'name',
                        'available',
                        'reason',
                        'message',
                    ]
                ]
            ])
            ->assertJson([
                'group_id' => $this->group->id,
                'group_name' => $this->group->name,
                'total_accommodations' => 2,
            ]);
    }

    public function test_validates_required_fields()
    {
        $token = $this->user->createToken('widget-token', ['widget-access']);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson("/api/accommodation-groups/{$this->group->id}/check-availability", []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['start_date', 'end_date']);
    }

    public function test_validates_date_logic()
    {
        $token = $this->user->createToken('widget-token', ['widget-access']);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson("/api/accommodation-groups/{$this->group->id}/check-availability", [
            'start_date' => now()->addDays(7)->format('Y-m-d'),
            'end_date' => now()->addDays(5)->format('Y-m-d'), // End before start
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['end_date']);
    }

    public function test_returns_404_for_nonexistent_group()
    {
        $token = $this->user->createToken('widget-token', ['widget-access']);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson("/api/accommodation-groups/999/check-availability", [
            'start_date' => now()->addDays(5)->format('Y-m-d'),
            'end_date' => now()->addDays(7)->format('Y-m-d'),
        ]);

        $response->assertStatus(404);
    }

    public function test_respects_minimum_stay_requirements()
    {
        $token = $this->user->createToken('widget-token', ['widget-access']);

        // accommodation2 has minimum_stay of 2, so 1 night should make it unavailable
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token->plainTextToken,
        ])->postJson("/api/accommodation-groups/{$this->group->id}/check-availability", [
            'start_date' => now()->addDays(5)->format('Y-m-d'),
            'end_date' => now()->addDays(6)->format('Y-m-d'), // Only 1 night
        ]);

        $response->assertStatus(200);

        $accommodations = $response->json('accommodations');
        
        // Find accommodation2 in the results
        $accommodation2Result = collect($accommodations)->firstWhere('id', $this->accommodation2->id);
        
        $this->assertNotNull($accommodation2Result);
        $this->assertFalse($accommodation2Result['available']);
        $this->assertEquals('minimum_stay', $accommodation2Result['reason']);
    }
}
